# -*- coding: utf-8 -*-
"""
Created on 29.11.2020

@author: <PERSON>
"""

from pandas import DataFrame
from requests import get as requests_get, exceptions as requests_exceptions
import koalafolio.gui.QLogger as logger

localLogger = logger.globalLogger

# staic class for static data
class ChaindataStatic:
    BLOCKDAEMON_BASE_URL = "https://svc.blockdaemon.com/"
    SUPPORTED_CHAINS = [
        "cardano",
        "ethereum",
        "solana",
        "polkadot",
        "polygon",
        "near",
        "avalanche"
    ]
    apiBaseURLs = {"cardano": BLOCKDAEMON_BASE_URL + "reporting/staking/v1/cardano/mainnet/delegator/history/",
                   "ethereum": BLOCKDAEMON_BASE_URL + "reporting/staking/v1/ethereum/mainnet/validator/history/",
                   "solana": BLOCKDAEMON_BASE_URL + "reporting/staking/v1/solana/mainnet/delegator/history/",
                   "polkadot": BL<PERSON>KDAEMON_BASE_URL + "reporting/staking/v1/polkadot/mainnet/nominator/history/",
                   "polygon": BLOCKDAEMON_BASE_URL + "reporting/staking/v1/polygon/mainnet/delegator/history/",
                   "near": BLOCKDAEMON_BASE_URL + "reporting/staking/v1/near/mainnet/delegator/history/",
                   "avalanche": BLOCKDAEMON_BASE_URL + "reporting/staking/v1/avalanche/mainnet/delegator/history/"}

    # time format factor, s = 1, ms = 1000, ...
    apiTimeFactors = {"cardano": 1, "ethereum": 1000, "solana": 1000, "polkadot": 1000, "polygon": 1, "near": 1,
                      "avalanche": 1}

    # timeUnit
    apiTimeUnits = {"cardano": "epoch", "ethereum": "week", "solana": "epoch", "polkadot": "era", "polygon": "epoch",
                    "near": "weekly", "avalanche": ""}

    @staticmethod
    def getBlockdaemonRewardsForAddress(apiname: str, apikey: str, address: str, start: int,
                                        end: int) -> pandas.DataFrame:
        startTimestamp = int(start) * ChaindataStatic.apiTimeFactors[apiname]
        endTimestamp = int(end) * ChaindataStatic.apiTimeFactors[apiname]
        if ChaindataStatic.apiTimeUnits[apiname]:
            payload = {
                "fromTime": startTimestamp,
                "toTime": endTimestamp,
                "timeUnit": ChaindataStatic.apiTimeUnits[apiname]
            }
        else:
            payload = {
                "fromTime": startTimestamp,
                "toTime": endTimestamp
            }

        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "X-API-Key": apikey
        }

        response = requests.post(ChaindataStatic.apiBaseURLs[apiname] + str(address), json=payload, headers=headers)
        try:
            content = response.json()
        except Exception as ex:
            localLogger.error("no data returned from Blockdaemon (" + str(apiname) + "): " + str(ex))
            return pandas.DataFrame()
        if "rewards" in content:
            rewards = content['rewards']
        else:
            rewards = content

        return pandas.DataFrame.from_dict(rewards)


